document.addEventListener('DOMContentLoaded', function() {
    const injectBtn = document.getElementById('injectBtn');
    const removeBtn = document.getElementById('removeBtn');
    const checkBtn = document.getElementById('checkBtn');
    const status = document.getElementById('status');
    
    function updateStatus(message, type = 'info') {
        status.textContent = message;
        status.className = `status ${type}`;
    }
    
    // 注入笑脸
    injectBtn.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.scripting.executeScript({
                target: {tabId: tabs[0].id},
                function: injectSmileyFunction
            }, function(result) {
                if (chrome.runtime.lastError) {
                    updateStatus('注入失败: ' + chrome.runtime.lastError.message, 'error');
                } else {
                    updateStatus('笑脸已成功注入！', 'success');
                }
            });
        });
    });
    
    // 移除笑脸
    removeBtn.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.scripting.executeScript({
                target: {tabId: tabs[0].id},
                function: removeSmileyFunction
            }, function(result) {
                if (chrome.runtime.lastError) {
                    updateStatus('移除失败: ' + chrome.runtime.lastError.message, 'error');
                } else {
                    updateStatus('笑脸已移除', 'success');
                }
            });
        });
    });
    
    // 检查状态
    checkBtn.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.scripting.executeScript({
                target: {tabId: tabs[0].id},
                function: checkSmileyFunction
            }, function(result) {
                if (chrome.runtime.lastError) {
                    updateStatus('检查失败: ' + chrome.runtime.lastError.message, 'error');
                } else if (result && result[0] && result[0].result) {
                    updateStatus('笑脸已存在于页面中', 'success');
                } else {
                    updateStatus('页面中未发现笑脸', 'info');
                }
            });
        });
    });
});

// 注入笑脸的函数
function injectSmileyFunction() {
    if (document.getElementById('global-injected-smiley')) {
        return '笑脸已存在';
    }
    
    const smiley = document.createElement('div');
    smiley.id = 'global-injected-smiley';
    smiley.innerHTML = '😊';
    smiley.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        font-size: 50px;
        z-index: 999999;
        cursor: pointer;
        animation: smileyBounce 2s infinite;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        padding: 15px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        transition: all 0.3s ease;
        user-select: none;
    `;
    
    smiley.onclick = function() {
        this.innerHTML = this.innerHTML === '😊' ? '😂' : '😊';
        this.style.transform = 'scale(1.5)';
        setTimeout(() => this.style.transform = 'scale(1)', 200);
    };
    
    const style = document.createElement('style');
    style.textContent = `
        @keyframes smileyBounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-20px); }
            60% { transform: translateY(-10px); }
        }
        #global-injected-smiley:hover {
            transform: scale(1.2) !important;
            box-shadow: 0 12px 40px rgba(0,0,0,0.4) !important;
        }
    `;
    
    document.head.appendChild(style);
    document.body.appendChild(smiley);
    
    return '笑脸注入成功';
}

// 移除笑脸的函数
function removeSmileyFunction() {
    const smiley = document.getElementById('global-injected-smiley');
    if (smiley) {
        smiley.remove();
        return '笑脸已移除';
    }
    return '未找到笑脸元素';
}

// 检查笑脸是否存在的函数
function checkSmileyFunction() {
    return !!document.getElementById('global-injected-smiley');
}
