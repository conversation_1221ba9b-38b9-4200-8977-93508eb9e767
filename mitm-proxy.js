const http = require('http');
const httpProxy = require('http-proxy-middleware');
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();
const PORT = 8080;
const TARGET_URL = 'https://rl3f8kl9-3000.asse.devtunnels.ms';

// 注入的笑脸代码
const SMILEY_INJECTION = `
<div id="global-injected-smiley" style="
    position: fixed;
    top: 20px;
    right: 20px;
    font-size: 50px;
    z-index: 999999;
    cursor: pointer;
    animation: smileyBounce 2s infinite;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    padding: 15px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    user-select: none;
" onclick="
    this.innerHTML = this.innerHTML === '😊' ? '😂' : '😊';
    this.style.transform = 'scale(1.5)';
    setTimeout(() => this.style.transform = 'scale(1)', 200);
" title="点击我切换表情！">😊</div>

<style>
    @keyframes smileyBounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-20px); }
        60% { transform: translateY(-10px); }
    }
    
    #global-injected-smiley:hover {
        transform: scale(1.2) !important;
        box-shadow: 0 12px 40px rgba(0,0,0,0.4) !important;
    }
</style>
`;

// 创建代理中间件
const proxyMiddleware = createProxyMiddleware({
    target: TARGET_URL,
    changeOrigin: true,
    secure: false,
    onProxyRes: function (proxyRes, req, res) {
        // 拦截HTML响应并注入代码
        if (proxyRes.headers['content-type'] && 
            proxyRes.headers['content-type'].includes('text/html')) {
            
            let body = '';
            proxyRes.on('data', function (chunk) {
                body += chunk;
            });
            
            proxyRes.on('end', function () {
                // 在</body>标签前注入笑脸代码
                const modifiedBody = body.replace('</body>', SMILEY_INJECTION + '</body>');
                
                // 更新Content-Length
                res.setHeader('Content-Length', Buffer.byteLength(modifiedBody));
                res.end(modifiedBody);
                
                console.log('✅ 已注入笑脸代码到页面');
            });
            
            // 阻止原始响应发送
            proxyRes.removeAllListeners('data');
            proxyRes.removeAllListeners('end');
        }
    },
    onError: function (err, req, res) {
        console.error('代理错误:', err);
        res.status(500).send('代理服务器错误');
    }
});

// 使用代理中间件
app.use('/', proxyMiddleware);

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 MITM代理服务器启动在端口 ${PORT}`);
    console.log(`📝 访问 http://localhost:${PORT} 来查看修改后的网站`);
    console.log(`🎯 目标网站: ${TARGET_URL}`);
    console.log(`😊 笑脸将被自动注入到所有HTML页面`);
});
