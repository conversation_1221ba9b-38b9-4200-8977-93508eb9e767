const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const { Transform } = require('stream');

const app = express();
const PORT = 8080;
const TARGET_URL = 'https://rl3f8kl9-3000.asse.devtunnels.ms';

// 注入的笑脸代码
const SMILEY_INJECTION = `
            <div id="global-injected-smiley" style="
                position: fixed;
                top: 20px;
                right: 20px;
                font-size: 50px;
                z-index: 999999;
                cursor: pointer;
                animation: smileyBounce 2s infinite;
                background: rgba(255, 255, 255, 0.9);
                border-radius: 50%;
                padding: 15px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
                user-select: none;
            " onclick="
                this.innerHTML = this.innerHTML === '😊' ? '😂' : '😊';
                this.style.transform = 'scale(1.5)';
                setTimeout(() => this.style.transform = 'scale(1)', 200);
            " title="点击我切换表情！">😊</div>

            <style>
                @keyframes smileyBounce {
                    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                    40% { transform: translateY(-20px); }
                    60% { transform: translateY(-10px); }
                }

                #global-injected-smiley:hover {
                    transform: scale(1.2) !important;
                    box-shadow: 0 12px 40px rgba(0,0,0,0.4) !important;
                }
            </style>

            <div style="
                position: fixed;
                top: 20px;
                left: 20px;
                font-size: 45px;
                z-index: 999999;
                animation: rotate 3s linear infinite;
                cursor: pointer;
                background: rgba(255,255,255,0.9);
                border-radius: 50%;
                padding: 12px;
                box-shadow: 0 6px 20px rgba(0,0,0,0.2);
            " onclick="
                alert('这是从未来页面来的笑脸！🚀');
                this.style.animation = 'bounce 1s ease';
            ">🚀</div>

            <style>
                @keyframes rotate {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
                @keyframes bounce {
                    0%, 100% { transform: translateY(0); }
                    50% { transform: translateY(-20px); }
                }
            </style>
        `;

// 创建HTML转换流
function createHtmlTransform() {
    return new Transform({
        transform(chunk, encoding, callback) {
            let html = chunk.toString();

            // 在</body>标签前注入代码
            if (html.includes('</body>')) {
                html = html.replace('</body>', SMILEY_INJECTION + '</body>');
                console.log('✅ 已注入笑脸代码到页面');
            }

            callback(null, html);
        }
    });
}

// 创建代理中间件
const proxyMiddleware = createProxyMiddleware({
    target: TARGET_URL,
    changeOrigin: true,
    secure: false,
    selfHandleResponse: true,
    onProxyRes: function (proxyRes, req, res) {
        console.log(`📥 代理响应: ${req.method} ${req.url} -> ${proxyRes.statusCode}`);

        // 复制响应头
        Object.keys(proxyRes.headers).forEach(key => {
            if (key !== 'content-length' && key !== 'content-encoding') {
                res.setHeader(key, proxyRes.headers[key]);
            }
        });

        res.statusCode = proxyRes.statusCode;

        // 检查是否是HTML响应
        const contentType = proxyRes.headers['content-type'] || '';
        if (contentType.includes('text/html')) {
            console.log('🔄 处理HTML响应，准备注入代码...');

            // 使用转换流处理HTML
            const htmlTransform = createHtmlTransform();
            proxyRes.pipe(htmlTransform).pipe(res);
        } else {
            // 非HTML响应直接转发
            proxyRes.pipe(res);
        }
    },
    onError: function (err, req, res) {
        console.error('❌ 代理错误:', err.message);
        if (!res.headersSent) {
            res.status(500).send('代理服务器错误: ' + err.message);
        }
    }
});

// 使用代理中间件
app.use('/', proxyMiddleware);

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 MITM代理服务器启动在端口 ${PORT}`);
    console.log(`📝 访问 http://localhost:${PORT} 来查看修改后的网站`);
    console.log(`🎯 目标网站: ${TARGET_URL}`);
    console.log(`😊 笑脸将被自动注入到所有HTML页面`);
});
