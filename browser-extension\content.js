// 内容脚本 - 自动注入笑脸
(function() {
    'use strict';
    
    console.log('🎯 笑脸注入器已激活');
    
    function injectSmiley() {
        // 检查是否已经注入过
        if (document.getElementById('global-injected-smiley')) {
            console.log('😊 笑脸已存在，跳过注入');
            return;
        }
        
        // 创建笑脸元素
        const smiley = document.createElement('div');
        smiley.id = 'global-injected-smiley';
        smiley.innerHTML = '😊';
        smiley.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            font-size: 50px;
            z-index: 999999;
            cursor: pointer;
            animation: smileyBounce 2s infinite;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            padding: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            user-select: none;
        `;
        
        // 添加点击事件
        smiley.onclick = function() {
            this.innerHTML = this.innerHTML === '😊' ? '😂' : '😊';
            this.style.transform = 'scale(1.5)';
            setTimeout(() => this.style.transform = 'scale(1)', 200);
        };
        
        smiley.title = '点击我切换表情！';
        
        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes smileyBounce {
                0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                40% { transform: translateY(-20px); }
                60% { transform: translateY(-10px); }
            }
            
            #global-injected-smiley:hover {
                transform: scale(1.2) !important;
                box-shadow: 0 12px 40px rgba(0,0,0,0.4) !important;
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(smiley);
        
        console.log('✅ 笑脸已成功注入');
        
        // 添加额外的火箭图标
        addRocketIcon();
    }
    
    function addRocketIcon() {
        const rocket = document.createElement('div');
        rocket.innerHTML = '🚀';
        rocket.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            font-size: 45px;
            z-index: 999999;
            animation: rotate 3s linear infinite;
            cursor: pointer;
            background: rgba(255,255,255,0.9);
            border-radius: 50%;
            padding: 12px;
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        `;
        
        rocket.onclick = function() {
            alert('这是从未来页面来的笑脸！🚀');
            this.style.animation = 'bounce 1s ease';
        };
        
        const rocketStyle = document.createElement('style');
        rocketStyle.textContent = `
            @keyframes rotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            @keyframes bounce {
                0%, 100% { transform: translateY(0); }
                50% { transform: translateY(-20px); }
            }
        `;
        
        document.head.appendChild(rocketStyle);
        document.body.appendChild(rocket);
    }
    
    // 等待页面加载完成后注入
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', injectSmiley);
    } else {
        injectSmiley();
    }
    
    // 监听动态内容变化
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // 检查是否需要重新注入
                if (!document.getElementById('global-injected-smiley')) {
                    setTimeout(injectSmiley, 100);
                }
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
})();
